"""
Amazon SES configuration for email notifications.
"""

from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_ses as ses,
)
from constructs import Construct


class SESConstruct(Construct):
    """Configure SES for sending email notifications."""

    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        sender_email: str = "<EMAIL>",
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id)

        self.sender_email = sender_email

        # Email identity (for production, use domain identity)
        # For now, we'll use email address identity for simplicity
        self.email_identity = ses.CfnEmailIdentity(
            self,
            "SenderEmailIdentity",
            email_identity=sender_email,
        )

        # Configuration set for tracking
        self.configuration_set = ses.CfnConfigurationSet(
            self,
            "EmailConfigurationSet",
            name="biormika-notifications",
            reputation_options=ses.CfnConfigurationSet.ReputationOptionsProperty(
                reputation_metrics_enabled=True
            ),
            sending_options=ses.CfnConfigurationSet.SendingOptionsProperty(
                sending_enabled=True
            ),
            suppression_options=ses.CfnConfigurationSet.SuppressionOptionsProperty(
                suppressed_reasons=["COMPLAINT", "BOUNCE"]
            ),
        )

        # Email templates
        self.analysis_complete_template = ses.CfnTemplate(
            self,
            "AnalysisCompleteTemplate",
            template=ses.CfnTemplate.TemplateProperty(
                template_name="biormika-analysis-complete",
                subject_part="HFO Analysis Complete - {{filename}}",
                html_part="""
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #1a73e8; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: white; padding: 20px; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
        .results-box { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .metric { display: inline-block; margin: 10px 20px 10px 0; }
        .metric-label { color: #666; font-size: 12px; text-transform: uppercase; }
        .metric-value { font-size: 24px; font-weight: bold; color: #1a73e8; }
        .button { display: inline-block; padding: 12px 24px; background: #1a73e8; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
        .button:hover { background: #1557b0; }
        .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>HFO Analysis Complete</h1>
        </div>
        <div class="content">
            <p>Dear User,</p>
            <p>Your HFO analysis has been successfully completed for:</p>
            <h3>{{filename}}</h3>

            <div class="results-box">
                <h4>Analysis Results Summary</h4>
                <div class="metric">
                    <div class="metric-label">HFOs Detected</div>
                    <div class="metric-value">{{hfo_count}}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Channels Analyzed</div>
                    <div class="metric-value">{{channel_count}}</div>
                </div>
                <div class="metric">
                    <div class="metric-label">Processing Time</div>
                    <div class="metric-value">{{processing_time}}s</div>
                </div>
                <div class="metric">
                    <div class="metric-label">HFO Density</div>
                    <div class="metric-value">{{hfo_density}}</div>
                </div>
            </div>

            <p>You can view the detailed results and interactive visualizations:</p>

            <div style="text-align: center;">
                <a href="{{results_url}}" class="button">View Interactive Results</a>
                <a href="{{download_url}}" class="button" style="background: #34a853;">Download Report</a>
            </div>

            <div class="footer">
                <p>This is an automated message from Biormika HFO Analysis System.</p>
                <p>Job ID: {{job_id}} | Completed at: {{completed_at}}</p>
            </div>
        </div>
    </div>
</body>
</html>
            """,
            text_part="""
HFO Analysis Complete

Your HFO analysis has been successfully completed for: {{filename}}

Results Summary:
- HFOs Detected: {{hfo_count}}
- Channels Analyzed: {{channel_count}}
- Processing Time: {{processing_time}}s
- HFO Density: {{hfo_density}}

View Results: {{results_url}}
Download Report: {{download_url}}

Job ID: {{job_id}}
Completed at: {{completed_at}}

This is an automated message from Biormika HFO Analysis System.
            """,
            )
        )

        # Batch complete template
        self.batch_complete_template = ses.CfnTemplate(
            self,
            "BatchCompleteTemplate",
            template=ses.CfnTemplate.TemplateProperty(
                template_name="biormika-batch-complete",
                subject_part="Batch HFO Analysis Complete - {{batch_count}} files processed",
                html_part="""
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 700px; margin: 0 auto; padding: 20px; }
        .header { background: #1a73e8; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: white; padding: 20px; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
        .summary { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .status-complete { color: #34a853; }
        .status-failed { color: #ea4335; }
        .button { display: inline-block; padding: 12px 24px; background: #1a73e8; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
        .view-link { color: #1a73e8; text-decoration: none; }
        .view-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Batch HFO Analysis Complete</h1>
        </div>
        <div class="content">
            <p>Dear User,</p>
            <p>Your batch HFO analysis has been completed.</p>

            <div class="summary">
                <h3>Batch Summary</h3>
                <p><strong>Total Files:</strong> {{batch_count}}</p>
                <p><strong>Successful:</strong> {{success_count}}</p>
                <p><strong>Failed:</strong> {{failed_count}}</p>
                <p><strong>Total Processing Time:</strong> {{total_time}}</p>
            </div>

            <h3>Individual Results:</h3>
            <table>
                <thead>
                    <tr>
                        <th>File</th>
                        <th>Status</th>
                        <th>HFOs</th>
                        <th>Processing Time</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each files}}
                    <tr>
                        <td>{{this.filename}}</td>
                        <td class="status-{{this.status}}">{{this.status}}</td>
                        <td>{{this.hfo_count}}</td>
                        <td>{{this.processing_time}}s</td>
                        <td><a href="{{this.results_url}}" class="view-link">View</a></td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>

            <div style="text-align: center;">
                <a href="{{dashboard_url}}" class="button">View All Results</a>
            </div>
        </div>
    </div>
</body>
</html>
            """,
            text_part="""
Batch HFO Analysis Complete

Your batch HFO analysis has been completed.

Batch Summary:
- Total Files: {{batch_count}}
- Successful: {{success_count}}
- Failed: {{failed_count}}
- Total Processing Time: {{total_time}}

View all results at: {{dashboard_url}}

This is an automated message from Biormika HFO Analysis System.
            """,
            )
        )

        # Error notification template
        self.error_template = ses.CfnTemplate(
            self,
            "AnalysisErrorTemplate",
            template=ses.CfnTemplate.TemplateProperty(
                template_name="biormika-analysis-error",
                subject_part="HFO Analysis Failed - {{filename}}",
                html_part="""
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #ea4335; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: white; padding: 20px; border: 1px solid #ddd; border-radius: 0 0 8px 8px; }
        .error-box { background: #fce8e6; padding: 15px; border-radius: 4px; margin: 20px 0; border-left: 4px solid #ea4335; }
        .footer { text-align: center; color: #666; font-size: 12px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>HFO Analysis Failed</h1>
        </div>
        <div class="content">
            <p>Dear User,</p>
            <p>Unfortunately, the HFO analysis failed for:</p>
            <h3>{{filename}}</h3>

            <div class="error-box">
                <h4>Error Details</h4>
                <p><strong>Error:</strong> {{error_message}}</p>
                <p><strong>Job ID:</strong> {{job_id}}</p>
                <p><strong>Failed at:</strong> {{failed_at}}</p>
            </div>

            <p>Please try the following:</p>
            <ul>
                <li>Verify the EDF file is valid and not corrupted</li>
                <li>Check that the file size is within limits (max 2GB)</li>
                <li>Ensure the file contains valid EEG data</li>
            </ul>

            <p>If the problem persists, please contact support with the Job ID.</p>

            <div class="footer">
                <p>This is an automated message from Biormika HFO Analysis System.</p>
            </div>
        </div>
    </div>
</body>
</html>
            """,
            text_part="""
HFO Analysis Failed

Unfortunately, the HFO analysis failed for: {{filename}}

Error Details:
- Error: {{error_message}}
- Job ID: {{job_id}}
- Failed at: {{failed_at}}

Please verify your file and try again. If the problem persists, contact support.

This is an automated message from Biormika HFO Analysis System.
            """,
            )
        )

    def grant_send_email(self, principal: iam.IGrantable) -> None:
        """Grant permission to send emails using SES."""
        principal.add_to_principal_policy(
            iam.PolicyStatement(
                actions=[
                    "ses:SendEmail",
                    "ses:SendTemplatedEmail",
                    "ses:SendBulkTemplatedEmail",
                ],
                resources=[
                    f"arn:aws:ses:{self.node.scope.region}:{self.node.scope.account}:identity/{self.sender_email}",
                    f"arn:aws:ses:{self.node.scope.region}:{self.node.scope.account}:configuration-set/*",
                    f"arn:aws:ses:{self.node.scope.region}:{self.node.scope.account}:template/*",
                ],
            )
        )
