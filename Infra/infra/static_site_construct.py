from aws_cdk import Duration, RemovalPolicy
from aws_cdk import aws_cloudfront as cloudfront
from aws_cdk import aws_cloudfront_origins as origins
from aws_cdk import aws_s3 as s3
from constructs import Construct

from .config import DEFAULT_ALLOWED_ORIGINS
from .helpers import create_cfn_output


class StaticSiteConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        domain_name: str | None = None,
        certificate_arn: str | None = None,
    ) -> None:
        super().__init__(scope, construct_id)

        self.domain_name = domain_name
        self.certificate_arn = certificate_arn

        # Create S3 bucket for static site
        self._create_s3_bucket()

        # Create CloudFront distribution
        self._create_cloudfront_distribution()

        # Create outputs
        self._create_outputs()

    def _create_s3_bucket(self) -> None:
        """Create S3 bucket for static website hosting."""
        self.site_bucket = s3.Bucket(
            self,
            "StaticSiteBucket",
            bucket_name=None,  # Let CDK generate unique name
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            removal_policy=RemovalPolicy.DESTROY,
            auto_delete_objects=True,
            versioned=False,
            cors=[
                s3.CorsRule(
                    allowed_headers=["*"],
                    allowed_methods=[s3.HttpMethods.GET, s3.HttpMethods.HEAD],
                    allowed_origins=DEFAULT_ALLOWED_ORIGINS,
                    exposed_headers=[],
                    max_age=3600,
                )
            ],
        )

    def _create_cloudfront_distribution(self) -> None:
        """Create CloudFront distribution for the static site."""
        # Create OAI for CloudFront to access S3
        oai = cloudfront.OriginAccessIdentity(
            self, "OriginAccessIdentity", comment="OAI for Biormika Static Site"
        )

        # Grant OAI read access to bucket
        self.site_bucket.grant_read(oai)

        # Create S3 origin with OAI
        s3_origin = origins.S3BucketOrigin.with_origin_access_identity(
            self.site_bucket, origin_access_identity=oai
        )

        # Custom error responses for SPA routing
        error_responses = [
            cloudfront.ErrorResponse(
                http_status=403,
                response_http_status=200,
                response_page_path="/index.html",
                ttl=Duration.minutes(5),
            ),
            cloudfront.ErrorResponse(
                http_status=404,
                response_http_status=200,
                response_page_path="/index.html",
                ttl=Duration.minutes(5),
            ),
        ]

        # Cache policies
        cache_policy = cloudfront.CachePolicy(
            self,
            "StaticSiteCachePolicy",
            cache_policy_name="BiormikaStaticSiteCachePolicy",
            default_ttl=Duration.hours(24),
            max_ttl=Duration.days(365),
            min_ttl=Duration.seconds(0),
            enable_accept_encoding_brotli=True,
            enable_accept_encoding_gzip=True,
            header_behavior=cloudfront.CacheHeaderBehavior.none(),
            query_string_behavior=cloudfront.CacheQueryStringBehavior.none(),
            cookie_behavior=cloudfront.CacheCookieBehavior.none(),
        )

        # Create distribution
        self.distribution = cloudfront.Distribution(
            self,
            "StaticSiteDistribution",
            default_root_object="index.html",
            default_behavior=cloudfront.BehaviorOptions(
                origin=s3_origin,
                viewer_protocol_policy=cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
                cache_policy=cache_policy,
                allowed_methods=cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
                compress=True,
            ),
            error_responses=error_responses,
            price_class=cloudfront.PriceClass.PRICE_CLASS_100,
            enabled=True,
            http_version=cloudfront.HttpVersion.HTTP2_AND_3,
            minimum_protocol_version=cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
            comment="Biormika Frontend Static Site Distribution",
        )

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs."""
        create_cfn_output(
            self,
            "DistributionId",
            value=self.distribution.distribution_id,
            description="CloudFront Distribution ID",
        )

        create_cfn_output(
            self,
            "DistributionDomainName",
            value=self.distribution.distribution_domain_name,
            description="CloudFront Distribution Domain Name",
        )

        create_cfn_output(
            self,
            "SiteBucketName",
            value=self.site_bucket.bucket_name,
            description="S3 Bucket Name for Static Site",
        )

        create_cfn_output(
            self,
            "CloudFrontURL",
            value=f"https://{self.distribution.distribution_domain_name}",
            description="CloudFront URL for accessing the site",
        )
